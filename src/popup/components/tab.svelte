<script lang="ts">
	import ForwardIcon from "@/assets/svg/forward-icon.svelte";

	import MinusIcon from "@/assets/svg/minus-icon.svelte";
	import MutedIcon from "@/assets/svg/muted-icon.svelte";
	import NextIcon from "@/assets/svg/next-icon.svelte";
	import PauseIcon from "@/assets/svg/pause-icon.svelte";
	import PlayIcon from "@/assets/svg/play-icon.svelte";
	import PlusIcon from "@/assets/svg/plus-icon.svelte";
	import PrevIcon from "@/assets/svg/prev-icon.svelte";
	import RewindIcon from "@/assets/svg/rewind-icon.svelte";
	import ShuffleIcon from "@/assets/svg/shuffle-icon.svelte";
	import VolumeIcon from "@/assets/svg/volume-icon.svelte";
	import type { ITab } from "@/interfaces/tab";
	import { YtEvents, dispatchYtEvent, seekVideoYt, setYtVolume, shuffleYt, type SeekVideoYtArgs, type SetYtVolumeArgs } from "@/utils/yt-utils";
	import Button from "./button.svelte";
	import ArrowRight from "./icons/arrow-right.svelte";
	import VideoPlaylist from "./video-playlist.svelte";
	import { secondToTimeString } from "@/utils/second-to-time-string";

	export let tab: ITab;
	export let tabs: ITab[];
	export let idx: number;
	export let hidePlaylist: boolean = false; // 控制是否隐藏播放列表功能

	let showPlaylist = idx === 0 && tab.isYoutube && tab.isPlaylist && !hidePlaylist;
	let youtubeVolume = tab.volume;
	let seekPosition = tab.duration === 0 ? 0 : (tab.currentTime / tab.duration) * 100;

	const toggleMute = async (tabId: number, state: boolean) => {
		await chrome.tabs.update(tabId, { muted: !state });
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isMuted = !state;
			}
			return tab;
		});
	};

	const refreshTabs = () => {
		setTimeout(async () => {
			const newTab = await chrome.tabs.get(tab.id);
			tabs = tabs.map((el) => {
				if (el.id === tab.id) el.title = newTab.title ?? "";
				return el;
			});
		}, 2000);
	};

	const setVolume = async () => {
		await chrome.scripting.executeScript<SetYtVolumeArgs, number>({
			func: setYtVolume,
			target: { tabId: tab.id },
			world: "MAIN",
			args: [youtubeVolume],
		});
	};

	const next = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.next()],
		});

		refreshTabs();
	};

	const prev = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.prev()],
		});

		refreshTabs();
	};

	const togglePlay = async (tabId: number) => {
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isPaused = !tab.isPaused;
			}
			return tab;
		});
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [tab.isYoutubeMusic ? YtEvents.playToggleMusic() : YtEvents.playToggle()],
		});
	};



	const toggleShuffle = async (tabId: number) => {
		if (tab.isYoutubeMusic) {
			await chrome.scripting.executeScript({
				func: dispatchYtEvent,
				target: { tabId },
				args: [YtEvents.shuffleMusic()],
			});
			return;
		}

		await chrome.scripting.executeScript({
			func: shuffleYt,
			target: { tabId },
			world: "MAIN",
		});
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isShuffled = !tab.isShuffled;
			}
			return tab;
		});
	};

	const seekVideo = async (tabId: number, second: number, to?: number) => {
		await chrome.scripting.executeScript<SeekVideoYtArgs, void>({
			func: seekVideoYt,
			target: { tabId },
			world: "MAIN",
			args: [{ second, to }],
		});
	};

	const openTab = async () => {
		await chrome.tabs.update(tab.id, {
			active: true,
		});
	};
</script>

<div class="flex w-full justify-between border border-gray-200 dark:border-gray-600 p-4 gap-3 rounded-xl flex-col
			 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700
			 shadow-lg hover:shadow-xl transition-all duration-200 relative">
	<button on:click={openTab} title="Open Tab" class="neu-open-tab-btn absolute top-3 right-3">
		<ArrowRight width={16} />
	</button>
	<div class="flex items-start gap-2 min-w-0 pr-10">
		{#if tab.iconUrl}
			<img width="18px" src={tab.iconUrl} alt="icon" />
		{/if}
		<span class="text-sm font-medium text-gray-800 dark:text-gray-200 break-words leading-relaxed" title={tab.title}>
			{tab.title}
		</span>
	</div>
	{#if tab.duration}
		<div class="flex items-center gap-3">
			<span class="text-sm text-gray-700 dark:text-gray-300 min-w-[50px] text-center">{secondToTimeString(Math.floor((seekPosition / 100) * tab.duration))}</span>
			<input
				bind:value={seekPosition}
				type="range"
				min="0"
				max={100}
				class="neu-progress-bar flex-1"
				on:change={() => seekVideo(tab.id, 0, (seekPosition / 100) * tab.duration)}
			/>
			<span class="text-sm text-gray-700 dark:text-gray-300 min-w-[50px] text-center">{secondToTimeString(Math.floor(tab.duration))}</span>
		</div>
	{/if}
	<div class="flex items-center justify-between w-full">
		<!-- 左侧：音量控制组 -->
		<div class="flex gap-6 items-center">
			<!-- 静音按钮 -->
			<button
				title="Mute/Unmute"
				on:click={() => toggleMute(tab.id, tab.isMuted)}
				class="neu-media-btn"
			>
				{#if tab.isMuted}
					<MutedIcon width={16} />
				{:else}
					<VolumeIcon width={16} />
				{/if}
			</button>

			{#if tab.isYoutube}
				<!-- 音量控制 -->
				<div class="flex gap-3 items-center">
					<input bind:value={youtubeVolume} type="range" min="0" max="100" class="neu-volume-slider w-20" on:change={setVolume} />
					<span class="neu-volume-display">{Math.floor(youtubeVolume)}</span>
				</div>
			{/if}
		</div>

		{#if tab.isYoutube}
			<!-- 中央：主要媒体控制按钮组 -->
			<div class="flex items-center justify-center gap-4">
				<!-- 快退按钮 -->
				<button
					title="Rewind 10s"
					class="neu-media-btn"
					on:click={() => seekVideo(tab.id, -10)}
				>
					<RewindIcon width={16} />
				</button>

				<!-- 播放/暂停按钮 - 稍大一些作为主要按钮 -->
				<button
					title="Play/Pause"
					class="neu-media-btn-primary"
					on:click={() => togglePlay(tab.id)}
				>
					{#if tab.isPaused}
						<PlayIcon width={20} />
					{:else}
						<PauseIcon width={20} />
					{/if}
				</button>

				<!-- 快进按钮 -->
				<button
					title="Forward 10s"
					class="neu-media-btn"
					on:click={() => seekVideo(tab.id, 10)}
				>
					<ForwardIcon width={16} />
				</button>
			</div>

			<!-- 右侧：播放列表控制 -->
			<div class="flex gap-3 items-center">
				<!-- Prev按钮 - 仅在播放列表时显示且未隐藏播放列表功能 -->
				{#if tab.isPlaylist && !hidePlaylist}
					<button
						title="Prev"
						class="neu-media-btn-small"
						on:click={() => prev(tab.id)}
					>
						<PrevIcon width={14} />
					</button>
				{/if}

				<!-- Shuffle按钮 -->
				{#if tab.isPlaylist && !tab.isPlaylistMix && !hidePlaylist}
					<button
						title={`Shuffle ${tab.isShuffled ? "ON" : "OFF"}`}
						class={`neu-media-btn-small ${tab.isShuffled ? "neu-media-btn-active" : ""}`}
						on:click={() => toggleShuffle(tab.id)}
					>
						<ShuffleIcon width={14} />
					</button>
				{/if}

				<!-- 播放列表展开按钮 -->
				{#if tab.isPlaylist && !hidePlaylist}
					<button
						title="Toggle Playlist"
						class={`neu-media-btn-small ${showPlaylist ? "neu-media-btn-active" : ""}`}
						on:click={() => (showPlaylist = !showPlaylist)}
					>
						<span class={`text-sm transition-transform duration-200 ${showPlaylist ? "" : "-rotate-90"}`}>🔽</span>
					</button>
				{/if}
			</div>
		{/if}
	</div>
	{#if showPlaylist && !hidePlaylist}
		<VideoPlaylist {refreshTabs} tabId={tab.id} />
	{/if}
</div>

<style>
	/* 轻拟物风格媒体按钮 */
	:global(.neu-media-btn) {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 0.75rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #6b7280;
		fill: #6b7280;
		background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
		transition: all 0.3s ease;
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1), -4px -4px 8px rgba(255, 255, 255, 0.8);
		border: none;
	}

	:global(.neu-media-btn:hover) {
		transform: translateY(-2px);
		box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.15), -6px -6px 12px rgba(255, 255, 255, 0.9);
		color: #4b5563;
		fill: #4b5563;
	}

	:global(.neu-media-btn:active) {
		transform: translateY(0);
		box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.2), inset -3px -3px 6px rgba(255, 255, 255, 0.8);
	}

	/* 主要播放按钮 - 稍大一些 */
	:global(.neu-media-btn-primary) {
		width: 3rem;
		height: 3rem;
		border-radius: 0.875rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #f97316;
		fill: #f97316;
		background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
		transition: all 0.3s ease;
		box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.12), -5px -5px 10px rgba(255, 255, 255, 0.8);
		border: none;
	}

	:global(.neu-media-btn-primary:hover) {
		transform: translateY(-2px);
		box-shadow: 7px 7px 14px rgba(0, 0, 0, 0.18), -7px -7px 14px rgba(255, 255, 255, 0.9);
		color: #ea580c;
		fill: #ea580c;
	}

	:global(.neu-media-btn-primary:active) {
		transform: translateY(0);
		box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.2), inset -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	/* 小尺寸媒体按钮 - 用于播放列表控制 */
	:global(.neu-media-btn-small) {
		width: 2rem;
		height: 2rem;
		border-radius: 0.5rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #6b7280;
		fill: #6b7280;
		background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
		transition: all 0.3s ease;
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.1), -3px -3px 6px rgba(255, 255, 255, 0.8);
		border: none;
	}

	:global(.neu-media-btn-small:hover) {
		transform: translateY(-1px);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.15), -4px -4px 8px rgba(255, 255, 255, 0.9);
		color: #4b5563;
		fill: #4b5563;
	}

	:global(.neu-media-btn-small:active) {
		transform: translateY(0);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* Open Tab按钮 */
	:global(.neu-open-tab-btn) {
		width: 2rem;
		height: 2rem;
		border-radius: 0.5rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #6b7280;
		fill: #6b7280;
		background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
		transition: all 0.3s ease;
		box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1), -2px -2px 4px rgba(255, 255, 255, 0.8);
		border: none;
	}

	:global(.neu-open-tab-btn:hover) {
		transform: translateY(-1px);
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.15), -3px -3px 6px rgba(255, 255, 255, 0.9);
		color: #f97316;
		fill: #f97316;
	}

	:global(.neu-open-tab-btn:active) {
		transform: translateY(0);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 激活状态的按钮 */
	:global(.neu-media-btn-active) {
		color: #3b82f6 !important;
		fill: #3b82f6 !important;
		background: linear-gradient(145deg, #dbeafe, #bfdbfe) !important;
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.1), inset -2px -2px 4px rgba(255, 255, 255, 0.8) !important;
	}

	/* 进度条轻拟物风格 */
	:global(.neu-progress-bar) {
		appearance: none;
		height: 0.5rem;
		border-radius: 0.25rem;
		background: linear-gradient(145deg, #e5e7eb, #f3f4f6);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.1), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
		outline: none;
		transition: all 0.2s ease;
	}

	:global(.neu-progress-bar::-webkit-slider-thumb) {
		appearance: none;
		width: 1rem;
		height: 1rem;
		border-radius: 50%;
		background: linear-gradient(145deg, #f97316, #ea580c);
		box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2), -2px -2px 4px rgba(255, 255, 255, 0.8);
		cursor: pointer;
		transition: all 0.2s ease;
	}

	:global(.neu-progress-bar::-webkit-slider-thumb:hover) {
		transform: scale(1.1);
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.25), -3px -3px 6px rgba(255, 255, 255, 0.9);
	}

	/* 音量滑块 */
	:global(.neu-volume-slider) {
		appearance: none;
		height: 0.375rem;
		border-radius: 0.1875rem;
		background: linear-gradient(145deg, #e5e7eb, #f3f4f6);
		box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.1), inset -1px -1px 2px rgba(255, 255, 255, 0.8);
		outline: none;
	}

	:global(.neu-volume-slider::-webkit-slider-thumb) {
		appearance: none;
		width: 0.75rem;
		height: 0.75rem;
		border-radius: 50%;
		background: linear-gradient(145deg, #3b82f6, #2563eb);
		box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2), -1px -1px 2px rgba(255, 255, 255, 0.8);
		cursor: pointer;
	}

	/* 音量显示 */
	:global(.neu-volume-display) {
		background: linear-gradient(145deg, #dbeafe, #bfdbfe);
		color: #1e40af;
		width: 35px;
		border-radius: 0.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.1), inset -1px -1px 2px rgba(255, 255, 255, 0.8);
		font-weight: 600;
		font-size: 0.75rem;
	}

	/* 深色模式 */
	:global(.dark) :global(.neu-media-btn) {
		color: #d1d5db;
		fill: #d1d5db;
		background: linear-gradient(145deg, #4b5563, #374151);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-media-btn:hover) {
		box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.4), -6px -6px 12px rgba(255, 255, 255, 0.08);
		color: #f3f4f6;
		fill: #f3f4f6;
	}

	:global(.dark) :global(.neu-media-btn:active) {
		box-shadow: inset 3px 3px 6px rgba(0, 0, 0, 0.3), inset -3px -3px 6px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-primary) {
		color: #fb923c;
		fill: #fb923c;
		background: linear-gradient(145deg, #4b5563, #374151);
		box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.35), -5px -5px 10px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-media-btn-primary:hover) {
		box-shadow: 7px 7px 14px rgba(0, 0, 0, 0.45), -7px -7px 14px rgba(255, 255, 255, 0.08);
		color: #f97316;
		fill: #f97316;
	}

	:global(.dark) :global(.neu-media-btn-primary:active) {
		box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.3), inset -4px -4px 8px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-small) {
		color: #d1d5db;
		fill: #d1d5db;
		background: linear-gradient(145deg, #4b5563, #374151);
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3), -3px -3px 6px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-media-btn-small:hover) {
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.4), -4px -4px 8px rgba(255, 255, 255, 0.08);
		color: #f3f4f6;
		fill: #f3f4f6;
	}

	:global(.dark) :global(.neu-media-btn-small:active) {
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-open-tab-btn) {
		color: #d1d5db;
		fill: #d1d5db;
		background: linear-gradient(145deg, #4b5563, #374151);
		box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), -2px -2px 4px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-open-tab-btn:hover) {
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4), -3px -3px 6px rgba(255, 255, 255, 0.08);
		color: #fb923c;
		fill: #fb923c;
	}

	:global(.dark) :global(.neu-open-tab-btn:active) {
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-active) {
		color: #60a5fa !important;
		fill: #60a5fa !important;
		background: linear-gradient(145deg, #1e3a8a, #1e40af) !important;
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1) !important;
	}

	:global(.dark) :global(.neu-progress-bar) {
		background: linear-gradient(145deg, #374151, #4b5563);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-progress-bar::-webkit-slider-thumb) {
		background: linear-gradient(145deg, #fb923c, #f97316);
		box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4), -2px -2px 4px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-volume-slider) {
		background: linear-gradient(145deg, #374151, #4b5563);
		box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.3), inset -1px -1px 2px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-volume-slider::-webkit-slider-thumb) {
		background: linear-gradient(145deg, #60a5fa, #3b82f6);
		box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4), -1px -1px 2px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-volume-display) {
		background: linear-gradient(145deg, #1e3a8a, #1e40af);
		color: #dbeafe;
		box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.3), inset -1px -1px 2px rgba(255, 255, 255, 0.05);
	}
</style>