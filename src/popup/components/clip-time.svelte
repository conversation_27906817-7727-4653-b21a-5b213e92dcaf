<script lang="ts">
	import type { IClipTime } from '../interfaces/clip-time';
	import { secondToTimeString } from '../../utils/second-to-time-string';

	export let clip: IClipTime;

	function init(el: HTMLInputElement) {
		el.focus();
	}

	// 获取当前播放时间 - 使用直接脚本注入方式
	async function getCurrentTime(type: 'start' | 'end') {
		try {
			// 1. 获取当前活动标签页
			const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
			if (tabs.length === 0) {
				console.warn('无法获取当前标签页');
				return;
			}

			const tabId = tabs[0].id!;

			// 2. 检查是否为YouTube页面
			if (!tabs[0].url?.includes('youtube.com/watch')) {
				console.warn('当前页面不是YouTube视频页面');
				return;
			}

			console.log('正在获取视频时间戳...');

			// 3. 关键：直接注入脚本获取视频信息，避免消息传递
			const result = await chrome.scripting.executeScript({
				target: { tabId },
				func: () => {
					// 这个函数在目标页面的上下文中执行
					const video = document.querySelector('video') as HTMLVideoElement;
					if (!video) {
						return { success: false, error: '页面中未找到视频元素' };
					}

					return {
						success: true,
						data: {
							currentTime: video.currentTime,
							duration: video.duration,
							isPlaying: !video.paused,
							readyState: video.readyState
						}
					};
				},
				world: 'MAIN' // 重要：在主世界执行，确保能访问页面的video元素
			});

			// 4. 处理返回结果
			if (!result[0]?.result?.success) {
				console.error('获取视频信息失败:', result[0]?.result?.error || '未知错误');
				return;
			}

			const videoData = result[0].result.data;
			const timeString = secondToTimeString(videoData.currentTime);

			console.log('获取到视频时间:', timeString, '(', videoData.currentTime, '秒)');

			// 智能填入逻辑：如果没有开始时间，优先填入开始时间
			if (type === 'start' || (!clip.start.trim() && type === 'end')) {
				clip.start = timeString;
				console.log('设置开始时间:', timeString);
			} else if (type === 'end') {
				clip.end = timeString;
				console.log('设置结束时间:', timeString);
			}

		} catch (error) {
			console.error('获取当前播放时间失败:', error);
		}
	}


</script>

<div class="bg-glass-light dark:bg-glass-dark backdrop-blur-glass rounded-2xl p-5 border border-white/20 dark:border-white/10 shadow-neu-inset dark:shadow-neu-inset-dark">
	<!-- 片段名称输入框 -->
	<div class="w-full mb-4">
		<input
			class="w-full p-3 rounded-xl bg-white/60 dark:bg-gray-800/60 border-none shadow-neu-inset dark:shadow-neu-inset-dark text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 dark:focus:ring-indigo-500/50 transition-all duration-200"
			type="text"
			placeholder="片段名称 (非必填)"
			bind:value={clip.name}
		/>
	</div>

	<!-- 时间输入框 -->
	<div class="flex gap-3 w-full">
		<div class="flex-1">
			<div class="flex gap-2">
				<input
					use:init
					class="flex-1 p-3 rounded-xl bg-white/60 dark:bg-gray-800/60 border-none shadow-neu-inset dark:shadow-neu-inset-dark text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 dark:focus:ring-indigo-500/50 transition-all duration-200"
					type="text"
					placeholder="Start HH:MM:SS"
					bind:value={clip.start}
				/>
				<button
					class="neu-time-btn"
					title="获取当前播放时间"
					on:click={() => getCurrentTime('start')}
				>
					📍
				</button>
			</div>
		</div>
		<div class="flex-1">
			<div class="flex gap-2">
				<input
					class="flex-1 p-3 rounded-xl bg-white/60 dark:bg-gray-800/60 border-none shadow-neu-inset dark:shadow-neu-inset-dark text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 dark:focus:ring-indigo-500/50 transition-all duration-200"
					type="text"
					placeholder="End HH:MM:SS"
					bind:value={clip.end}
				/>
				<button
					class="neu-time-btn"
					title="获取当前播放时间"
					on:click={() => getCurrentTime('end')}
				>
					📍
				</button>
			</div>
		</div>
	</div>
</div>

<style>
	.neu-time-btn {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 0.5rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 0.875rem;
		color: #4b5563;
		background: linear-gradient(to bottom right, #e5e7eb, #d1d5db);
		transition: all 0.2s ease;
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1), -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	.neu-time-btn:hover {
		transform: translateY(-2px);
		box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.15), -6px -6px 12px rgba(255, 255, 255, 0.9);
	}

	.neu-time-btn:active {
		transform: translateY(0);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	:global(.dark) .neu-time-btn {
		color: #d1d5db;
		background: linear-gradient(to bottom right, #4b5563, #374151);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) .neu-time-btn:hover {
		box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.4), -6px -6px 12px rgba(255, 255, 255, 0.08);
	}

	:global(.dark) .neu-time-btn:active {
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1);
	}
</style>
