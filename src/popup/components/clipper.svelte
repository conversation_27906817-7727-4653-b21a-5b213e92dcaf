<script lang="ts">
	import { onMount } from 'svelte';
	import { secondToTimeString } from '../../utils/second-to-time-string';
	import { timeStringToSecond } from '../../utils/time-string-to-second';
	import { storage } from '../stores/storage';
	import ClipTime from './clip-time.svelte';
	import type { IClipTime } from '../interfaces/clip-time';
	import type { IVideoClip } from '../../interfaces/clip-time';
	import AddButton from './add-button.svelte';
	import RegionSelector from './region-selector.svelte';
	import type { IDelogoRegion } from '../../interfaces/video-preview';
	import { calculatePreviewDimensions } from '../../interfaces/video-preview';

	import Tab from './tab.svelte';
	import type { ITab } from '../../interfaces/tab';

	export let tab: chrome.tabs.Tab;
	export let id: string;

	// Tab组件数据
	let tabData: ITab | null = null;
	let tabs: ITab[] = []; // Tab组件需要的tabs数组

	// Delogo 区域选择相关状态
	let showRegionSelector = false;
	let delogoRegions: IDelogoRegion[] = [];
	let videoWidth = 1920;
	let videoHeight = 1080;

	// 预览窗口尺寸配置
	const MAX_PREVIEW_WIDTH = 800;
	const MAX_PREVIEW_HEIGHT = 450;

	// 计算动态预览尺寸，保持视频宽高比
	$: previewDimensions = calculatePreviewDimensions(
		videoWidth,
		videoHeight,
		MAX_PREVIEW_WIDTH,
		MAX_PREVIEW_HEIGHT
	);

	// 编辑模式相关状态
	let editMode = false;
	let editingClip: IVideoClip | null = null;
	let editingClipIndex = -1;
	let editingClipName = '';
	let hasUnsavedChanges = false; // 跟踪是否有未保存的更改



	let clips: IClipTime[] = [
		{
			start: '',
			end: '',
			name: '',
		},
	];

	let message = '';
	let isSaving = false; // 保存状态

	$: isCanSave = !!clips.find((clip) => clip.start || clip.end || clip.name.trim());

	/**
	 * 获取当前tab的媒体控制数据
	 */
	async function fetchTabData() {
		if (!tab.id || !tab.url?.includes('youtube.com/watch')) {
			return;
		}

		const isYoutubeMusic = !!tab.url?.includes("music.youtube.co");
		const isYoutube = isYoutubeMusic || tab.url?.includes("youtube.com/watch") || tab.url?.includes("youtube.com/shorts");
		const isPlaylist = isYoutubeMusic || !!tab.url?.includes("list=");

		try {
			const res = await chrome.scripting.executeScript<
				{ isPlaylist: boolean; isYoutubeMusic: boolean }[],
				{
					volume: number;
					isPaused: boolean;
					isShuffled: boolean;
					isPlaylistMix: boolean;
					duration: number;
					currentTime: number;
					videoWidth: number;
					videoHeight: number;
					videoResolution: string;
				}
			>({
				func: (
					...args: {
						isPlaylist: boolean;
						isYoutubeMusic: boolean;
					}[]
				) => {
					const video = document.getElementById("movie_player") as any;
					let isPlaylistMix = false;
					if (args[0].isPlaylist) {
						const playlistTitle =
							Array.from(document.querySelectorAll("#container.ytd-playlist-panel-renderer"))
								.reverse()
								.find((el) => el.children.length !== 0)
								?.querySelector(".header")
								?.querySelector(".title")?.innerHTML ?? "";
						isPlaylistMix = playlistTitle.includes("Mix -");
					}

					const shuffleButton = document.querySelector('[aria-label="Shuffle playlist"]');
					const isShuffled = shuffleButton?.getAttribute("aria-pressed") === "true";

					// 获取视频分辨率信息
					const videoElement = document.querySelector("video") as HTMLVideoElement;
					const videoWidth = videoElement?.videoWidth ?? 0;
					const videoHeight = videoElement?.videoHeight ?? 0;
					const videoResolution = videoWidth > 0 && videoHeight > 0 ? `${videoWidth}×${videoHeight}` : '未知';

					return {
						volume: video?.getVolume() ?? 0,
						isPaused: videoElement?.paused ?? false,
						isShuffled,
						isPlaylistMix,
						duration: video?.getDuration() ?? 0,
						currentTime: video?.getCurrentTime() ?? 0,
						videoWidth,
						videoHeight,
						videoResolution,
					};
				},
				target: { tabId: tab.id! },
				world: "MAIN",
				args: [{ isPlaylist, isYoutubeMusic }],
			});

			tabData = {
				isMuted: !!tab.mutedInfo?.muted,
				isYoutube: true,
				isYoutubeMusic,
				title: tab.title ?? "",
				volume: res[0].result.volume,
				isPlaylist,
				id: tab.id ?? 0,
				isPaused: res[0].result?.isPaused,
				iconUrl: tab.favIconUrl,
				isShuffled: res[0].result.isShuffled,
				isPlaylistMix: res[0].result.isPlaylistMix,
				currentTime: res[0].result.currentTime,
				duration: res[0].result.duration,
			};

			// 更新视频尺寸
			if (res[0].result.videoWidth && res[0].result.videoHeight) {
				videoWidth = res[0].result.videoWidth;
				videoHeight = res[0].result.videoHeight;
			}

			// 更新tabs数组供Tab组件使用
			tabs = [tabData];

		} catch (error) {
			console.warn(`无法获取标签页媒体信息:`, error);
			// 创建默认的tab数据
			tabData = {
				isMuted: !!tab.mutedInfo?.muted,
				isYoutube: true,
				isYoutubeMusic,
				title: tab.title ?? "",
				volume: 0,
				isPlaylist,
				id: tab.id ?? 0,
				isPaused: true,
				iconUrl: tab.favIconUrl,
				isShuffled: false,
				isPlaylistMix: false,
				currentTime: 0,
				duration: 0,
			};
			tabs = [tabData];
		}
	}

	/**
	 * 立即更新tab数据 - 用于媒体控制操作后的实时更新
	 */
	async function updateTabData() {
		await fetchTabData();
	}

	onMount(async () => {
		// 检查是否为编辑模式
		const editInfo = sessionStorage.getItem('clipperEditInfo');
		if (editInfo) {
			try {
				const parsedEditInfo = JSON.parse(editInfo);
				if (parsedEditInfo.mode === 'edit' && parsedEditInfo.videoId === id) {
					editMode = true;
					editingClip = parsedEditInfo.clip;
					editingClipIndex = parsedEditInfo.clipIndex;
					editingClipName = editingClip?.name || '';

					// 从片段中加载delogo区域配置
					if (editingClip?.delogoRegions) {
						delogoRegions = [...editingClip.delogoRegions];
					} else {
						delogoRegions = [];
					}

					// 自动打开delogo界面
					showRegionSelector = true;

					// 清除sessionStorage中的编辑信息
					sessionStorage.removeItem('clipperEditInfo');
				}
			} catch (error) {
				console.error('解析编辑信息失败:', error);
			}
		}

		// 新的工作流程：剪藏页面始终显示空的输入框
		// 已保存的片段只在"记录"页面显示
		const video = $storage.videos[id];
		if (video) {
			// 只加载视频尺寸，不加载delogo区域（因为delogo现在是片段级别的）
			if (video.videoWidth && video.videoHeight) {
				videoWidth = video.videoWidth;
				videoHeight = video.videoHeight;
			}
		}



		// 获取Tab媒体控制数据
		await fetchTabData();

		// 设置定时更新Tab数据（每3秒更新一次）
		updateInterval = setInterval(async () => {
			if (tabData && !showRegionSelector) { // 只在不显示区域选择器时更新
				await fetchTabData();
			}
		}, 3000);

		// 清理函数
		return () => {
			if (updateInterval) {
				clearInterval(updateInterval);
			}
		};
	});

	async function saveVideo() {
		isSaving = true; // 开始保存状态

		const videoClips: IVideoClip[] = [];
		for (let idx = 0; idx < clips.length; idx++) {
			const clip = clips[idx];
			const canSave = clip.start || clip.end || clip.name.trim();
			if (!canSave) {
				continue;
			}
			const startSeconds = timeStringToSecond(clip.start);
			if (clip.start && startSeconds === -1) {
				message = 'Start time is not valid';
				isSaving = false;
				return;
			}

			const endSeconds = timeStringToSecond(clip.end);
			if (clip.end && endSeconds === -1) {
				message = 'End time is not valid';
				isSaving = false;
				return;
			}

			if (clip.start && clip.end && endSeconds <= startSeconds) {
				message = 'End time should be greater than start time';
				isSaving = false;
				return;
			}

			if (idx > 0) {
				if (startSeconds <= videoClips[idx - 1].end || videoClips[idx - 1].end === -1) {
					message = `Start must be greater than the end of prev clip`;
					isSaving = false;
					return;
				}
			}

			videoClips.push({
				start: startSeconds,
				end: endSeconds,
				name: clip.name.trim() || `片段${idx + 1}`,  // 如果没有名称，使用默认名称
				delogoRegions: []  // 新片段默认没有delogo区域
			});
		}

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();

			// 如果视频已存在，进行去重合并
			if (prev.videos[id]) {
				const existingClips = prev.videos[id].clips;
				const mergedClips = [...existingClips];

				// 对新片段进行去重检查（只有开始和结束时间完全相同才视为重复）
				for (const newClip of videoClips) {
					const isDuplicate = existingClips.some(existingClip =>
						existingClip.start === newClip.start &&
						existingClip.end === newClip.end
					);

					if (!isDuplicate) {
						mergedClips.push(newClip);
					}
				}

				// 按开始时间排序
				mergedClips.sort((a, b) => a.start - b.start);

				prev.videos[id] = {
					...prev.videos[id],
					clips: mergedClips,
					videoWidth: videoWidth,       // 保存视频宽度
					videoHeight: videoHeight      // 保存视频高度
				};
			} else {
				// 新视频，直接保存
				prev.videos[id] = {
					id,
					title: tab.title!,
					clips: videoClips,
					videoWidth: videoWidth,       // 保存视频宽度
					videoHeight: videoHeight      // 保存视频高度
				};
			}
			return prev;
		});

		// 保存成功后清空所有输入框，等待下一次输入
		clearUnsavedClips();

		// 显示对号2秒后恢复
		setTimeout(() => {
			isSaving = false;
		}, 2000);
	}

	// 修改为只清除未保存的片段输入，不删除已保存的视频记录
	function clearUnsavedClips() {
		// 重置所有片段输入框为空
		for (let i = 0; i < clips.length; i++) {
			clips[i].start = '';
			clips[i].end = '';
			clips[i].name = '';
		}
		// 重置为只有一个空片段
		clips = [{
			start: '',
			end: '',
			name: '',
		}];
		// 不再显示清除消息
	}

	function addClip() {
		clips.push({
			end: '',
			start: '',
			name: '',
		});
		clips = [...clips];
	}

	/**
	 * 打开区域选择器
	 */
	function openRegionSelector() {
		showRegionSelector = true;
	}

	/**
	 * 关闭区域选择器
	 */
	function closeRegionSelector() {
		showRegionSelector = false;

		// 如果是编辑模式，重置编辑状态并返回记录页面
		if (editMode) {
			editMode = false;
			editingClip = null;
			editingClipIndex = -1;
			editingClipName = '';

			// 通知父组件切换回记录页面
			window.dispatchEvent(new CustomEvent('switchToRecords'));
		}
	}

	/**
	 * 处理区域添加
	 */
	function handleRegionAdd(event: CustomEvent) {
		const region = event.detail as IDelogoRegion;
		delogoRegions = [...delogoRegions, region];
		message = `已添加区域: ${region.name}`;
		hasUnsavedChanges = true; // 标记有未保存的更改

		// 在编辑模式下，区域只在内存中保存，不立即写入storage
		// 只有在saveClipChanges中才会保存到片段级别
	}

	/**
	 * 处理区域删除
	 */
	function handleRegionDelete(event: CustomEvent) {
		const regionId = event.detail as string;
		delogoRegions = delogoRegions.filter(r => r.id !== regionId);
		message = '已删除区域';
		hasUnsavedChanges = true; // 标记有未保存的更改

		// 在编辑模式下，区域只在内存中删除，不立即写入storage
		// 只有在saveClipChanges中才会保存到片段级别
	}

	/**
	 * 处理区域切换
	 */
	function handleRegionToggle(event: CustomEvent) {
		const { regionId, enabled } = event.detail;
		const region = delogoRegions.find(r => r.id === regionId);
		if (region) {
			region.enabled = enabled;
			delogoRegions = [...delogoRegions];
			message = `区域 ${region.name} ${enabled ? '已启用' : '已禁用'}`;
			hasUnsavedChanges = true; // 标记有未保存的更改

			// 在编辑模式下，区域只在内存中切换，不立即写入storage
			// 只有在saveClipChanges中才会保存到片段级别
		}
	}

	/**
	 * 清除所有区域
	 */
	function handleRegionsClear() {
		delogoRegions = [];
		message = '已清除所有区域';
		hasUnsavedChanges = true; // 标记有未保存的更改

		// 在编辑模式下，区域只在内存中清除，不立即写入storage
		// 只有在saveClipChanges中才会保存到片段级别
	}



	/**
	 * 处理视频尺寸更新
	 */
	function handleVideoSizeUpdate(event: CustomEvent) {
		const { width, height } = event.detail;
		videoWidth = width;
		videoHeight = height;

		// 在编辑模式下不显示视频尺寸检测消息，避免干扰用户
		if (!editMode) {
			message = `检测到视频尺寸: ${width}×${height}`;
		}

		// 在编辑模式下，视频尺寸只在内存中更新，不立即写入storage
		// 只有在saveClipChanges中才会保存到存储中
		// 这样可以避免频繁的Firebase写入操作
	}

	/**
	 * 处理视频捕获开始
	 */
	function handleCaptureStart() {
		message = '视频预览已启动';
	}

	/**
	 * 处理区域选择器错误
	 */
	function handleRegionError(event: CustomEvent) {
		const errorMessage = event.detail as string;
		message = `错误: ${errorMessage}`;
	}

	/**
	 * 保存片段修改（重命名和delogo区域）
	 */
	function saveClipChanges() {
		if (!editMode || !editingClip || editingClipIndex === -1) {
			message = '编辑信息无效';
			console.error('保存失败：编辑信息无效', { editMode, editingClip, editingClipIndex });
			return;
		}



		// 更新存储中的片段信息
		storage.update((prev) => {
			if (prev.videos[id] && prev.videos[id].clips && prev.videos[id].clips[editingClipIndex]) {
				// 更新片段名称
				if (editingClipName.trim()) {
					prev.videos[id].clips[editingClipIndex].name = editingClipName.trim();
				}

				// 将delogo区域配置保存到片段级别
				prev.videos[id].clips[editingClipIndex].delogoRegions = [...delogoRegions];

				// 更新视频尺寸（保留在视频级别，用于预览）
				prev.videos[id].videoWidth = videoWidth;
				prev.videos[id].videoHeight = videoHeight;

				prev.lastSync = new Date().getTime();
			}
			return prev;
		});

		message = '片段修改已保存';
		hasUnsavedChanges = false; // 重置未保存更改标志

		// 关闭编辑模式并返回记录页面
		editMode = false;
		editingClip = null;
		editingClipIndex = -1;
		editingClipName = '';
		closeRegionSelector();

		// 通知父组件切换回记录页面
		window.dispatchEvent(new CustomEvent('switchToRecords'));
	}


</script>

<div class="w-full">
	<!-- Tab媒体控制卡片 -->
	{#if tabData}
		<div class="mb-4">
			<Tab tab={tabData} idx={0} bind:tabs hidePlaylist={true} onUpdateTabData={updateTabData} />
		</div>
	{:else}
		<!-- 备用显示：简单标题 -->
		<div class="flex gap-2 items-start mb-4">
			<img width="20px" src={tab.favIconUrl} alt="icon" />
			<span class="text-sm font-medium">{tab.title}</span>
		</div>
	{/if}


	<!-- 消息显示 - 只显示错误消息，不显示成功消息 -->
	{#if message != '' && !message.includes('Saved') && !message.includes('Cleared')}
		<div class="flex justify-center mt-3">
			<p class="text-red-500 font-medium text-sm">{message}</p>
		</div>
	{/if}
	<div class="flex flex-col mb-1 mt-2 gap-2 items-start">
		{#each clips as clip}
			<div class="w-full">
				<ClipTime bind:clip />
			</div>
		{/each}
		<!-- 对称按钮布局：Save - 增加片段 - Clear -->
		<div class="flex justify-center items-center w-full gap-4">
			<!-- Save 按钮 - 绿色渐变 -->
			<button
				disabled={!isCanSave || isSaving}
				on:click={saveVideo}
				class="px-6 py-2.5 rounded-xl font-semibold text-white shadow-lg hover:shadow-xl
					   transform hover:-translate-y-0.5 transition-all duration-200
					   flex items-center justify-center active:shadow-inner active:transform-none
					   {isCanSave && !isSaving ? 'bg-gradient-to-br from-green-500 to-green-700 hover:from-green-600 hover:to-green-800' : 'bg-gradient-to-br from-gray-400 to-gray-600 cursor-not-allowed opacity-60'}"
				title="保存片段">
				{#if isSaving}
					<!-- 对号图标 -->
					<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
						<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
					</svg>
				{:else}
					Save
				{/if}
			</button>

			<!-- 增加片段按钮 -->
			<div class="flex-1 max-w-xs">
				<AddButton onClick={addClip} />
			</div>

			<!-- Clear 按钮 - 红色渐变 -->
			<button
				on:click={clearUnsavedClips}
				class="px-6 py-2.5 rounded-xl font-semibold text-white shadow-lg hover:shadow-xl
					   transform hover:-translate-y-0.5 transition-all duration-200
					   flex items-center justify-center active:shadow-inner active:transform-none
					   bg-gradient-to-br from-red-500 to-red-700 hover:from-red-600 hover:to-red-800"
				title="清除未保存的片段输入">
				Clear
			</button>
		</div>
	</div>




</div>

<!-- 区域选择器模态框 -->
{#if showRegionSelector}
	<div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
		<div class="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-4xl w-full max-h-[95vh] overflow-hidden flex flex-col">
			<!-- 模态框头部 -->
			<div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
				<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
					{editMode ? '修改片段' : '设置去logo区域'}
				</h2>
				<button
					on:click={closeRegionSelector}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- 模态框主体 -->
			<div class="flex-1 overflow-auto p-4">
				<!-- 画面预览窗口（移到顶部） -->
				<div class="mb-6">
					<RegionSelector
						{videoWidth}
						{videoHeight}
						previewWidth={previewDimensions.width}
						previewHeight={previewDimensions.height}
						regions={delogoRegions}
						tabId={tab.id}
						on:regionAdd={handleRegionAdd}
						on:regionDelete={handleRegionDelete}
						on:regionToggle={handleRegionToggle}
						on:regionsClear={handleRegionsClear}
						on:videoSizeUpdate={handleVideoSizeUpdate}
						on:captureStart={handleCaptureStart}
						on:error={handleRegionError}
					/>
				</div>

				<!-- 片段重命名功能板块（仅在编辑模式下显示） -->
				{#if editMode}
					<div class="mb-6 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700
								rounded-xl p-4 border border-gray-200 dark:border-gray-600">
						<h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
							📝 片段重命名
						</h3>
						<div class="flex items-center gap-3">
							<input
								type="text"
								bind:value={editingClipName}
								placeholder="输入新的片段名称 (非必填)"
								class="flex-1 px-3 py-2 rounded-lg bg-white dark:bg-gray-800
									   border border-gray-300 dark:border-gray-600
									   text-gray-700 dark:text-gray-200
									   placeholder-gray-500 dark:placeholder-gray-400
									   focus:outline-none focus:ring-2 focus:ring-purple-500
									   transition-all duration-200"
								on:input={() => hasUnsavedChanges = true}
							/>
							<div class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
								当前: {editingClip?.name || `片段${editingClipIndex + 1}`}
							</div>
						</div>
					</div>
				{/if}

			</div>

			<!-- 模态框底部 -->
			<div class="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
				<div class="flex gap-2">
					{#if editMode && hasUnsavedChanges}
						<button
							on:click={saveClipChanges}
							class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700
								   transition-all duration-200 shadow-md hover:shadow-lg
								   transform hover:-translate-y-0.5"
						>
							💾 保存修改
						</button>
					{/if}
					<button
						on:click={closeRegionSelector}
						class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700
							   transition-all duration-200 shadow-md hover:shadow-lg
							   transform hover:-translate-y-0.5"
					>
						{editMode ? '取消' : '完成'}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}
