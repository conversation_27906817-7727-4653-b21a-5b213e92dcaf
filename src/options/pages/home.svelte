<script lang="ts">
	import { onMount } from 'svelte';
	import Button from '../components/button.svelte';
	import Input from '../components/input.svelte';
	import { storage } from '../stores/store';
	import Switch from '../components/switch.svelte';
	import { getSupportedBrowserOptions, type SupportedBrowser } from '../../utils/browser-detector';

	let message = '';

	let shuffle = false;
	let rewindTime: string = '';
	let forwardTime: string = '';
	let autoSkip: boolean = false;
	let enableCookiesByDefault: boolean = false;
	let defaultBrowser: SupportedBrowser | '' = '';
	
	// 获取支持的浏览器选项
	const supportedBrowsers = getSupportedBrowserOptions();

	onMount(() => {
		rewindTime = $storage.rewindTime!.toString();
		forwardTime = $storage.forwardTime!.toString();

		shuffle = $storage.alwaysShuffle!;
		autoSkip = $storage.autoSkipAd!;
		enableCookiesByDefault = $storage.enableCookiesByDefault || false;
		defaultBrowser = $storage.preferredBrowser || '';
	});

	const save = async () => {
		const rewind = parseInt(rewindTime);
		const forward = parseInt(forwardTime);
		if (isNaN(rewind) || rewind < 0) {
			message = 'Rewind time must be > 0';
			return;
		}
		if (isNaN(forward) || forward < 0) {
			message = 'Forward time must be > 0';
			return;
		}

		storage.update((prev) => {
			prev.lastSync = new Date().getTime();
			prev.includedUrls = ['youtube.com']; // 固定为youtube.com
			prev.rewindTime = rewind;
			prev.forwardTime = forward;

			prev.alwaysShuffle = shuffle;
			prev.autoSkipAd = autoSkip;
			prev.enableCookiesByDefault = enableCookiesByDefault;
			prev.preferredBrowser = defaultBrowser;
			return prev;
		});
		message = 'Saved';
	};
</script>

<div class="flex flex-col items-center gap-4">
	<div class="flex gap-2 w-full">
		<div class="w-full">
			<label for="included" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Rewind Time (s)</label>
			<Input bind:value={rewindTime} placeholder="10" />
		</div>
		<div class="w-full">
			<label for="included" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Forward Time (s)</label>
			<Input bind:value={forwardTime} placeholder="10" />
		</div>
	</div>

	<div class="flex items-center justify-between w-full">
		<span class="text-sm font-medium">Always shuffle Youtube playlist</span>
		<Switch bind:isChecked={shuffle} />
	</div>
	<div class="flex items-center justify-between w-full">
		<span class="text-sm font-medium">Auto Skip Youtube Ads</span>
		<Switch bind:isChecked={autoSkip} />
	</div>
	
	<!-- Cookie设置分区 -->
	<div class="w-full border-t pt-4 mt-4">
		<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Cookie Settings</h3>
		
		<div class="flex items-center justify-between w-full mb-4">
			<div class="flex flex-col">
				<span class="text-sm font-medium">Enable cookies by default</span>
				<span class="text-xs text-gray-500 dark:text-gray-400">Automatically enable cookie option in yt-dlp commands</span>
			</div>
			<Switch bind:isChecked={enableCookiesByDefault} />
		</div>
		
		<div class="w-full">
			<label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Default Browser for Cookies</label>
			<select 
				bind:value={defaultBrowser}
				class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
					   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
					   focus:outline-none focus:ring-2 focus:ring-blue-500"
			>
				<option value="">Auto-detect browser</option>
				{#each supportedBrowsers as browser}
					<option value={browser.value}>{browser.label}</option>
				{/each}
			</select>
			<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
				Leave blank to auto-detect browser. Manual selection overrides auto-detection.
			</p>
		</div>
	</div>
	
	<Button onClick={() => save()}>Save</Button>
	{#if message.length !== 0}
		<div>{message}</div>
	{/if}
</div>
